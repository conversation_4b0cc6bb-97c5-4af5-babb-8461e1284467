<?php
// Include database connection
require_once '../../includes/config/database.php';
require_once '../../includes/functions/functions.php';

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in, otherwise redirect to login
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../index.php");
    exit();
}

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Process filter parameters
$filterStatus = isset($_GET['status']) ? $_GET['status'] : '';
$filterPaymentStatus = isset($_GET['payment_status']) ? $_GET['payment_status'] : '';
$filterDocumentType = isset($_GET['document_type']) ? $_GET['document_type'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Initialize pagination variables
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$recordsPerPage = 10;
$offset = ($page - 1) * $recordsPerPage;

// Build the query based on filters
$whereClause = [];
$params = [];

if (!empty($filterStatus)) {
    $whereClause[] = "dr.status = ?";
    $params[] = $filterStatus;
}

if (!empty($filterPaymentStatus)) {
    $whereClause[] = "dr.payment_status = ?";
    $params[] = $filterPaymentStatus;
}

if (!empty($filterDocumentType)) {
    $whereClause[] = "dr.document_type = ?";
    $params[] = $filterDocumentType;
}

if (!empty($search)) {
    $whereClause[] = "(r.first_name LIKE ? OR r.last_name LIKE ? OR dr.reference_number LIKE ?)";
    $searchTerm = "%$search%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

// Construct the WHERE clause
$whereSQL = '';
if (!empty($whereClause)) {
    $whereSQL = "WHERE " . implode(" AND ", $whereClause);
}

// Get total number of records
$countQuery = "SELECT COUNT(*)
               FROM document_requests dr
               JOIN residents r ON dr.resident_id = r.resident_id
               $whereSQL";

$countStmt = $conn->prepare($countQuery);
$countStmt->execute($params);
$totalRecords = $countStmt->fetchColumn();

$totalPages = ceil($totalRecords / $recordsPerPage);

// Get document requests with pagination
$query = "SELECT dr.*,
          CONCAT(r.first_name, ' ', r.last_name) AS resident_name,
          CASE
              WHEN o.official_id IS NOT NULL THEN
                  (SELECT CONCAT(r_official.first_name, ' ', r_official.last_name)
                   FROM residents r_official
                   WHERE r_official.resident_id = o.resident_id)
              ELSE 'Not Assigned'
          END AS official_name,
          IFNULL(dr.request_source, 'admin') AS request_source
          FROM document_requests dr
          JOIN residents r ON dr.resident_id = r.resident_id
          LEFT JOIN officials o ON dr.processed_by = o.official_id
          $whereSQL
          ORDER BY dr.request_date DESC
          LIMIT :offset, :limit";

$stmt = $conn->prepare($query);

// Bind parameters for where clause
foreach ($params as $key => $value) {
    $stmt->bindValue($key + 1, $value);
}

// Bind pagination parameters
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':limit', $recordsPerPage, PDO::PARAM_INT);

$stmt->execute();
$documentRequests = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get document types for filter dropdown
$docTypeQuery = "SELECT DISTINCT document_type FROM document_requests ORDER BY document_type";
$docTypeStmt = $conn->prepare($docTypeQuery);
$docTypeStmt->execute();
$documentTypes = $docTypeStmt->fetchAll(PDO::FETCH_COLUMN);

// Get active residents for add request dropdown
$residentsQuery = "SELECT resident_id, CONCAT(first_name, ' ', last_name) AS full_name
                  FROM residents
                  WHERE status = 'Active'
                  ORDER BY first_name";
$residentsStmt = $conn->prepare($residentsQuery);
$residentsStmt->execute();
$residents = $residentsStmt->fetchAll(PDO::FETCH_ASSOC);

// Define document types available
$availableDocTypes = [
    'Certificate' => 'Barangay Certificate',
    'Clearance' => 'Barangay Clearance',
    'Indigency' => 'Certificate of Indigency',
    'Residency' => 'Certificate of Residency',
    'Good_Standing' => 'Certificate of Good Standing',
    'No_Pending_Case' => 'Certificate of No Pending Case',
    'Demolition' => 'Certificate of Demolition',
    'Solo_Parents' => 'Solo Parents',
    'TODA_Certificate' => 'TODA Certificate'
];

// Define status options
$statusOptions = ['Pending', 'Processing', 'Ready for Pickup', 'Released', 'Cancelled', 'Rejected'];
$paymentStatusOptions = ['Unpaid', 'Paid'];

// Get document fees from settings
try {
    $feeQuery = "SELECT * FROM system_settings WHERE setting_name LIKE 'document_fee_%'";
    $feeStmt = $conn->prepare($feeQuery);
    $feeStmt->execute();
    $fees = $feeStmt->fetchAll(PDO::FETCH_ASSOC);

    $documentFees = [];
    foreach ($fees as $fee) {
        $docType = str_replace('document_fee_', '', $fee['setting_name']);
        $documentFees[$docType] = $fee['setting_value'];
    }
} catch (PDOException $e) {
    $documentFees = [
        'certificate' => 50,
        'clearance' => 50,
        'indigency' => 0,
        'residency' => 50,
        'certificate_of_transfer' => 50
    ];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Requests - Barangay Management System</title>
    <link rel="stylesheet" href="../../assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <!-- Add Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Add Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>
<body>
    <?php include '../../includes/header.php'; ?>
    <?php include '../../includes/sidebar.php'; ?>

    <div class="content-wrapper">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <h2>Document Requests</h2>

                    <?php if (isset($_SESSION['success_message'])): ?>
                        <div class="alert alert-success">
                            <?= $_SESSION['success_message'] ?>
                            <?php unset($_SESSION['success_message']); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_SESSION['error_message'])): ?>
                        <div class="alert alert-danger">
                            <?= $_SESSION['error_message'] ?>
                            <?php unset($_SESSION['error_message']); ?>
                        </div>
                    <?php endif; ?>

                    <div class="card mb-4">
                        <div class="card-header">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="card-title">Filter Requests</h5>
                                </div>
                                <div class="col-md-6 text-right">
                                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addRequestModal">
                                        <i class="fas fa-plus"></i> New Request
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <form method="GET" action="" class="mb-4">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="status">Status</label>
                                            <select class="form-control" id="status" name="status">
                                                <option value="">All Statuses</option>
                                                <?php foreach ($statusOptions as $option): ?>
                                                    <option value="<?= $option ?>" <?= ($filterStatus == $option) ? 'selected' : '' ?>>
                                                        <?= $option ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="payment_status">Payment Status</label>
                                            <select class="form-control" id="payment_status" name="payment_status">
                                                <option value="">All Payment Statuses</option>
                                                <?php foreach ($paymentStatusOptions as $option): ?>
                                                    <option value="<?= $option ?>" <?= ($filterPaymentStatus == $option) ? 'selected' : '' ?>>
                                                        <?= $option ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="document_type">Document Type</label>
                                            <select class="form-control" id="document_type" name="document_type">
                                                <option value="">All Document Types</option>
                                                <?php foreach ($documentTypes as $type): ?>
                                                    <option value="<?= $type ?>" <?= ($filterDocumentType == $type) ? 'selected' : '' ?>>
                                                        <?= $type ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="search">Search</label>
                                            <input type="text" class="form-control" id="search" name="search" placeholder="Name or Reference #" value="<?= htmlspecialchars($search) ?>">
                                        </div>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Apply Filters
                                </button>
                                <a href="document_requests.php" class="btn btn-secondary">
                                    <i class="fas fa-sync"></i> Reset
                                </a>
                            </form>

                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>Resident</th>
                                            <th>Document Type</th>
                                            <th>Reference #</th>
                                            <th>Request Date</th>
                                            <th>Status</th>
                                            <th>Payment</th>
                                            <th>Source</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (count($documentRequests) > 0): ?>
                                            <?php foreach ($documentRequests as $request): ?>
                                                <tr>
                                                    <td><?= $request['request_id'] ?></td>
                                                    <td><?= htmlspecialchars($request['resident_name']) ?></td>
                                                    <td><?= htmlspecialchars($request['document_type']) ?></td>
                                                    <td><?= htmlspecialchars($request['reference_number']) ?></td>
                                                    <td><?= date('M d, Y', strtotime($request['request_date'])) ?></td>
                                                    <td>
                                                        <span class="badge rounded-pill <?= getStatusBadgeClass($request['status']) ?>" style="padding: 8px 12px; font-size: 12px;">
                                                            <?= $request['status'] ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge rounded-pill <?= getPaymentStatusBadgeClass($request['payment_status']) ?>" style="padding: 8px 12px; font-size: 12px;">
                                                            <?= $request['payment_status'] ?>
                                                        </span>
                                                        <small>₱<?= number_format($request['payment_amount'], 2) ?></small>
                                                    </td>
                                                    <td>
                                                        <?php if (isset($request['request_source']) && $request['request_source'] == 'portal'): ?>
                                                            <span class="badge bg-primary" style="padding: 8px 12px; font-size: 12px;">
                                                                <i class="fas fa-globe"></i> Portal
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary" style="padding: 8px 12px; font-size: 12px;">
                                                                <i class="fas fa-user-shield"></i> Admin
                                                            </span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-info view-request"
                                                                    data-id="<?= $request['request_id'] ?>"
                                                                    data-resident="<?= htmlspecialchars($request['resident_name']) ?>"
                                                                    data-document="<?= htmlspecialchars($request['document_type']) ?>"
                                                                    data-purpose="<?= htmlspecialchars($request['purpose']) ?>"
                                                                    data-reference="<?= htmlspecialchars($request['reference_number']) ?>"
                                                                    data-requestdate="<?= date('F d, Y', strtotime($request['request_date'])) ?>"
                                                                    data-releasedate="<?= $request['release_date'] ? date('F d, Y', strtotime($request['release_date'])) : 'Not set' ?>"
                                                                    data-status="<?= htmlspecialchars($request['status']) ?>"
                                                                    data-paymentstatus="<?= htmlspecialchars($request['payment_status']) ?>"
                                                                    data-amount="<?= number_format($request['payment_amount'], 2) ?>"
                                                                    data-official="<?= htmlspecialchars($request['official_name'] ?? 'Not assigned') ?>"
                                                                    data-remarks="<?= htmlspecialchars($request['remarks'] ?? '') ?>"
                                                                    data-pickup-option="<?= htmlspecialchars($request['pickup_option'] ?? 'Pickup') ?>"
                                                                    data-delivery-address="<?= htmlspecialchars($request['delivery_address'] ?? '') ?>"
                                                                    data-file="<?= $request['document_file'] ? '../../uploads/documents/' . $request['document_file'] : '' ?>">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <?php if (isset($request['request_source']) && $request['request_source'] == 'portal'): ?>
                                                                <a href="process_portal_request.php?id=<?= $request['request_id'] ?>" class="btn btn-sm btn-primary">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                            <?php else: ?>
                                                                <a href="update_document.php?id=<?= $request['request_id'] ?>" class="btn btn-sm btn-primary">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                            <?php endif; ?>
                                                            <button type="button" class="btn btn-sm btn-danger delete-request"
                                                                    data-id="<?= $request['request_id'] ?>"
                                                                    data-reference="<?= htmlspecialchars($request['reference_number']) ?>"
                                                                    data-resident="<?= htmlspecialchars($request['resident_name']) ?>">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="9" class="text-center">No document requests found</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($totalPages > 1): ?>
                                <nav aria-label="Page navigation">
                                    <ul class="pagination justify-content-center">
                                        <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                                            <a class="page-link" href="?page=<?= $page - 1 ?><?= getQueryParams($filterStatus, $filterPaymentStatus, $filterDocumentType, $search) ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>

                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                                                <a class="page-link" href="?page=<?= $i ?><?= getQueryParams($filterStatus, $filterPaymentStatus, $filterDocumentType, $search) ?>">
                                                    <?= $i ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>

                                        <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                                            <a class="page-link" href="?page=<?= $page + 1 ?><?= getQueryParams($filterStatus, $filterPaymentStatus, $filterDocumentType, $search) ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Request Modal -->
    <div class="modal fade" id="addRequestModal" tabindex="-1" role="dialog" aria-labelledby="addRequestModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRequestModalLabel">Add New Document Request</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addRequestForm" method="POST" action="document_actions.php">
                        <input type="hidden" name="action" value="add">

                        <div class="form-group">
                            <label for="resident_id">Resident</label>
                            <select class="form-control select2" id="resident_id" name="resident_id" required>
                                <option value="">Select Resident</option>
                                <?php foreach ($residents as $resident): ?>
                                    <option value="<?= $resident['resident_id'] ?>">
                                        <?= htmlspecialchars($resident['full_name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="document_type">Document Type</label>
                            <select class="form-control" id="document_type_select" name="document_type" required>
                                <option value="">Select Document Type</option>
                                <?php foreach ($availableDocTypes as $value => $label): ?>
                                    <option value="<?= $value ?>" data-fee="<?= $documentFees[strtolower($value)] ?? 50 ?>">
                                        <?= htmlspecialchars($label) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="purpose">Purpose</label>
                            <textarea class="form-control" id="purpose" name="purpose" rows="3" required></textarea>
                        </div>

                        <div class="form-group">
                            <label>Document Fee</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text">₱</span>
                                </div>
                                <input type="text" class="form-control" id="fee_display" readonly>
                            </div>
                            <small class="form-text text-muted">This fee is set by the system based on the document type.</small>
                        </div>

                        <div class="form-group">
                            <label for="remarks">Remarks (Optional)</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" form="addRequestForm" class="btn btn-primary">Submit Request</button>
                </div>
            </div>
        </div>
    </div>

    <!-- View Request Modal -->
    <div class="modal fade" id="viewRequestModal" tabindex="-1" role="dialog" aria-labelledby="viewRequestModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewRequestModalLabel">Document Request Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Request ID:</strong> <span id="view-id"></span></p>
                            <p><strong>Resident:</strong> <span id="view-resident"></span></p>
                            <p><strong>Document Type:</strong> <span id="view-document"></span></p>
                            <p><strong>Purpose:</strong> <span id="view-purpose"></span></p>
                            <p><strong>Reference Number:</strong> <span id="view-reference"></span></p>
                            <p><strong>Processed By:</strong> <span id="view-official"></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Request Date:</strong> <span id="view-requestdate"></span></p>
                            <p><strong>Release Date:</strong> <span id="view-releasedate"></span></p>
                            <p><strong>Status:</strong> <span id="view-status"></span></p>
                            <p><strong>Payment Status:</strong> <span id="view-paymentstatus"></span></p>
                            <p><strong>Payment Amount:</strong> ₱<span id="view-amount"></span></p>
                            <p><strong>Remarks:</strong> <span id="view-remarks"></span></p>
                        </div>
                    </div>

                    <!-- Delivery Information Section -->
                    <div class="row mt-3" id="delivery-info-section">
                        <div class="col-12">
                            <h6><strong>Pickup/Delivery Information:</strong></h6>
                            <p><strong>Option:</strong> <span id="view-pickup-option"></span></p>
                            <div id="delivery-address-container" style="display: none;">
                                <p><strong>Delivery Address:</strong> <span id="view-delivery-address"></span></p>
                            </div>
                        </div>
                    </div>

                    <div id="document-file-container" class="mt-3" style="display: none;">
                        <h6>Attached Document:</h6>
                        <div class="text-center">
                            <a id="view-file-link" href="#" target="_blank" class="btn btn-info">
                                <i class="fas fa-file-download"></i> View/Download Document
                            </a>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <a id="edit-request-link" href="#" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this document request?</p>
                    <p><strong>Reference #:</strong> <span id="delete-reference"></span></p>
                    <p><strong>Resident:</strong> <span id="delete-resident"></span></p>
                </div>
                <div class="modal-footer">
                    <form id="deleteForm" method="POST" action="document_actions.php">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" id="delete-id" name="request_id">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php include '../../includes/footer.php'; ?>

    <script src="../../assets/js/jquery.min.js"></script>
    <script src="../../assets/js/bootstrap.bundle.min.js"></script>
    <!-- Add Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2({
                width: '100%',
                placeholder: 'Select a resident',
                allowClear: true
            });

            // Update fee display when document type changes
            $('#document_type_select').change(function() {
                var fee = $(this).find(':selected').data('fee') || 0;
                $('#fee_display').val(fee.toFixed(2));
            });

            // View request modal
            $('.view-request').click(function() {
                $('#view-id').text($(this).data('id'));
                $('#view-resident').text($(this).data('resident'));
                $('#view-document').text($(this).data('document'));
                $('#view-purpose').text($(this).data('purpose'));
                $('#view-reference').text($(this).data('reference'));
                $('#view-requestdate').text($(this).data('requestdate'));
                $('#view-releasedate').text($(this).data('releasedate'));
                $('#view-status').text($(this).data('status'));
                $('#view-paymentstatus').text($(this).data('paymentstatus'));
                $('#view-amount').text($(this).data('amount'));
                $('#view-official').text($(this).data('official'));
                $('#view-remarks').text($(this).data('remarks') || 'None');

                // Handle pickup/delivery information
                var pickupOption = $(this).data('pickup-option') || 'Pickup';
                var deliveryAddress = $(this).data('delivery-address') || '';

                $('#view-pickup-option').text(pickupOption);

                if (pickupOption === 'Delivery' && deliveryAddress) {
                    $('#delivery-address-container').show();
                    $('#view-delivery-address').text(deliveryAddress);
                } else {
                    $('#delivery-address-container').hide();
                }

                // Set the edit link
                $('#edit-request-link').attr('href', 'update_document.php?id=' + $(this).data('id'));

                // Handle document file if available
                if ($(this).data('file')) {
                    $('#document-file-container').show();
                    $('#view-file-link').attr('href', $(this).data('file'));
                } else {
                    $('#document-file-container').hide();
                }

                $('#viewRequestModal').modal('show');
            });

            // Delete request modal
            $('.delete-request').click(function() {
                $('#delete-id').val($(this).data('id'));
                $('#delete-reference').text($(this).data('reference'));
                $('#delete-resident').text($(this).data('resident'));
                $('#deleteModal').modal('show');
            });
        });
    </script>
</body>
</html>

<?php
// Helper functions
function getStatusBadgeClass($status) {
    // Normalize status value (trim and convert to lowercase)
    $normalizedStatus = strtolower(trim($status));

    switch ($normalizedStatus) {
        case 'pending':
            return 'bg-warning text-dark';
        case 'processing':
            return 'bg-primary text-white';
        case 'ready for pickup':
            return 'bg-success text-white';
        case 'released':
            return 'bg-success text-white';
        case 'completed':
            return 'bg-success text-white';
        case 'cancelled':
            return 'bg-secondary text-white';
        case 'rejected':
            return 'bg-danger text-white';
        case 'approved':
            return 'bg-success text-white';
        default:
            // Fallback that handles partial matches
            if (strpos($normalizedStatus, 'reject') !== false) {
                return 'bg-danger text-white';
            } else if (strpos($normalizedStatus, 'approve') !== false) {
                return 'bg-success text-white';
            } else if (strpos($normalizedStatus, 'pend') !== false) {
                return 'bg-warning text-dark';
            } else if (strpos($normalizedStatus, 'complet') !== false) {
                return 'bg-success text-white';
            }
            return 'bg-secondary text-white';
    }
}

function getPaymentStatusBadgeClass($status) {
    if ($status === 'Paid') {
        return 'bg-success text-white';
    } else if ($status === 'Waived') {
        return 'bg-info text-white';
    } else {
        return 'bg-danger text-white';
    }
}

function getQueryParams($status, $paymentStatus, $documentType, $search) {
    $params = [];

    if (!empty($status)) {
        $params[] = "status=" . urlencode($status);
    }

    if (!empty($paymentStatus)) {
        $params[] = "payment_status=" . urlencode($paymentStatus);
    }

    if (!empty($documentType)) {
        $params[] = "document_type=" . urlencode($documentType);
    }

    if (!empty($search)) {
        $params[] = "search=" . urlencode($search);
    }

    return !empty($params) ? "&" . implode("&", $params) : "";
}