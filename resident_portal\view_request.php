<?php
// view_request.php - View details of a document request
session_start();

// Redirect to login if not logged in
if (!isset($_SESSION['resident_id'])) {
    header("Location: login.php");
    exit;
}

include '../includes/config/database.php';
include '../includes/functions/utility.php';

// Get request ID from URL
$request_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
if ($request_id <= 0) {
    header("Location: my_requests.php");
    exit();
}

// Get resident ID
$resident_id = $_SESSION['resident_id'];

// Fetch document request details (ensure resident owns the request)
try {
    $query = "SELECT dr.*, r.first_name, r.middle_name, r.last_name, r.address, r.contact_number, r.email
              FROM document_requests dr
              JOIN residents r ON dr.resident_id = r.resident_id
              WHERE dr.request_id = :request_id AND dr.resident_id = :resident_id";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':request_id', $request_id);
    $stmt->bindParam(':resident_id', $resident_id);
    $stmt->execute();
    $request = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$request) {
        $error_message = "Request not found or you do not have permission to view it.";
    }
} catch (PDOException $e) {
    $error_message = "Database error: " . $e->getMessage();
}

$page_title = "View Request Details";

// Check if this is an AJAX request for modal
$is_ajax = isset($_GET['ajax']) && $_GET['ajax'] == '1';

if ($is_ajax) {
    // Only output the modal body (no <html>, <head>, etc.)
    ?>
    <div class="modal-header bg-primary text-white">
        <h3 class="modal-title mb-0" id="requestDetailsModalLabel"><i class="fas fa-file-alt me-2"></i>Request Details</h3>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    <div class="modal-body">
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger"> <?php echo $error_message; ?> </div>
        <?php else: ?>
            <dl class="row">
                <dt class="col-sm-4">Request ID</dt>
                <dd class="col-sm-8">#<?php echo htmlspecialchars($request['request_id']); ?></dd>
                <dt class="col-sm-4">Document Type</dt>
                <dd class="col-sm-8"><?php echo htmlspecialchars($request['document_type']); ?></dd>
                <dt class="col-sm-4">Purpose</dt>
                <dd class="col-sm-8"><?php echo htmlspecialchars($request['purpose']); ?></dd>
                <dt class="col-sm-4">Status</dt>
                <dd class="col-sm-8"><span class="badge bg-info text-dark"><?php echo htmlspecialchars($request['status']); ?></span></dd>
                <dt class="col-sm-4">Date Requested</dt>
                <dd class="col-sm-8"><?php echo date('F d, Y g:i A', strtotime($request['request_date'])); ?></dd>
                <dt class="col-sm-4">Resident Name</dt>
                <dd class="col-sm-8"><?php echo htmlspecialchars($request['first_name'] . ' ' . ($request['middle_name'] ? $request['middle_name'] . ' ' : '') . $request['last_name']); ?></dd>
                <dt class="col-sm-4">Address</dt>
                <dd class="col-sm-8"><?php echo htmlspecialchars($request['address']); ?></dd>
                <dt class="col-sm-4">Contact Number</dt>
                <dd class="col-sm-8"><?php echo htmlspecialchars($request['contact_number']); ?></dd>
                <dt class="col-sm-4">Email</dt>
                <dd class="col-sm-8"><?php echo htmlspecialchars($request['email']); ?></dd>
            </dl>
            <?php if (!empty($request['remarks'])): ?>
                <div class="alert alert-info mt-4"><strong>Remarks:</strong> <?php echo nl2br(htmlspecialchars($request['remarks'])); ?></div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
    </div>
    <?php
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container py-5 text-center">
        <a href="my_requests.php" class="btn btn-outline-primary mb-4"><i class="fas fa-arrow-left me-2"></i>Back to My Requests</a>
    </div>
    <!-- Modal -->
    <div class="modal fade" id="requestDetailsModal" tabindex="-1" aria-labelledby="requestDetailsModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header bg-primary text-white">
            <h3 class="modal-title mb-0" id="requestDetailsModalLabel"><i class="fas fa-file-alt me-2"></i>Request Details</h3>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger"> <?php echo $error_message; ?> </div>
            <?php else: ?>
                <dl class="row">
                    <dt class="col-sm-4">Request ID</dt>
                    <dd class="col-sm-8">#<?php echo htmlspecialchars($request['request_id']); ?></dd>
                    <dt class="col-sm-4">Document Type</dt>
                    <dd class="col-sm-8"><?php echo htmlspecialchars($request['document_type']); ?></dd>
                    <dt class="col-sm-4">Purpose</dt>
                    <dd class="col-sm-8"><?php echo htmlspecialchars($request['purpose']); ?></dd>
                    <dt class="col-sm-4">Status</dt>
                    <dd class="col-sm-8"><span class="badge bg-info text-dark"><?php echo htmlspecialchars($request['status']); ?></span></dd>
                    <dt class="col-sm-4">Date Requested</dt>
                    <dd class="col-sm-8"><?php echo date('F d, Y g:i A', strtotime($request['request_date'])); ?></dd>
                    <dt class="col-sm-4">Resident Name</dt>
                    <dd class="col-sm-8"><?php echo htmlspecialchars($request['first_name'] . ' ' . ($request['middle_name'] ? $request['middle_name'] . ' ' : '') . $request['last_name']); ?></dd>
                    <dt class="col-sm-4">Address</dt>
                    <dd class="col-sm-8"><?php echo htmlspecialchars($request['address']); ?></dd>
                    <dt class="col-sm-4">Contact Number</dt>
                    <dd class="col-sm-8"><?php echo htmlspecialchars($request['contact_number']); ?></dd>
                    <dt class="col-sm-4">Email</dt>
                    <dd class="col-sm-8"><?php echo htmlspecialchars($request['email']); ?></dd>

                    <!-- Pickup/Delivery Information -->
                    <dt class="col-sm-4">Pickup Option</dt>
                    <dd class="col-sm-8">
                        <span class="badge bg-<?php echo ($request['pickup_option'] === 'Delivery') ? 'warning' : 'success'; ?>">
                            <i class="fas fa-<?php echo ($request['pickup_option'] === 'Delivery') ? 'truck' : 'store'; ?> me-1"></i>
                            <?php echo htmlspecialchars($request['pickup_option'] ?? 'Pickup'); ?>
                        </span>
                    </dd>

                    <?php if (!empty($request['delivery_address']) && $request['pickup_option'] === 'Delivery'): ?>
                    <dt class="col-sm-4">Delivery Address</dt>
                    <dd class="col-sm-8"><?php echo htmlspecialchars($request['delivery_address']); ?></dd>
                    <?php endif; ?>

                    <?php if (!empty($request['payment_amount'])): ?>
                    <dt class="col-sm-4">Payment Amount</dt>
                    <dd class="col-sm-8">
                        <span class="badge bg-primary">₱<?php echo number_format($request['payment_amount'], 2); ?></span>
                        <?php if ($request['pickup_option'] === 'Delivery'): ?>
                            <small class="text-muted">(includes ₱50.00 delivery fee)</small>
                        <?php endif; ?>
                    </dd>
                    <?php endif; ?>
                </dl>
                <?php if (!empty($request['remarks'])): ?>
                    <div class="alert alert-info mt-4"><strong>Remarks:</strong> <?php echo nl2br(htmlspecialchars($request['remarks'])); ?></div>
                <?php endif; ?>
            <?php endif; ?>
          </div>
          <div class="modal-footer">
            <a href="my_requests.php" class="btn btn-outline-primary"><i class="fas fa-arrow-left me-2"></i>Back to My Requests</a>
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        var modal = new bootstrap.Modal(document.getElementById('requestDetailsModal'));
        modal.show();
      });
    </script>
</body>
</html>
