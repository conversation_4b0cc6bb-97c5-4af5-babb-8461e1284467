<?php
// Include database connection
include '../../includes/session.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] != 'Admin') {
    header("Location: ../../index.php");
    exit();
}

$message = '';
$message_type = '';

// Check current ENUM values
try {
    $query = "SHOW COLUMNS FROM properties LIKE 'property_type'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $current_enum = $result['Type'];
    echo "<h3>Current property_type ENUM:</h3>";
    echo "<p><code>$current_enum</code></p>";
    
    // Check if Electronics is already in the ENUM
    $has_electronics = strpos($current_enum, 'Electronics') !== false;
    
    if ($has_electronics) {
        echo "<p style='color: green;'>✅ Electronics is already included in the ENUM.</p>";
    } else {
        echo "<p style='color: red;'>❌ Electronics is missing from the ENUM.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error checking ENUM: " . $e->getMessage() . "</p>";
}

// If form is submitted to update the ENUM
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_enum'])) {
    try {
        // Begin transaction
        $conn->beginTransaction();
        
        // Update the property_type ENUM to include Electronics
        $alter_query = "ALTER TABLE properties 
                        MODIFY COLUMN property_type 
                        ENUM('Land', 'Building', 'Vehicle', 'Equipment', 'Furniture', 'Electronics', 'Other') 
                        NOT NULL";
        
        $stmt = $conn->prepare($alter_query);
        $stmt->execute();
        
        // Commit the transaction
        $conn->commit();
        
        $message = "Property type ENUM updated successfully! Electronics has been added.";
        $message_type = "success";
        
        // Log the activity
        if (function_exists('logActivity')) {
            logActivity($conn, "Updated property_type ENUM to include Electronics", $_SESSION['user_id'], 'properties', 'Database Update');
        }
        
    } catch (PDOException $e) {
        // Rollback on error
        $conn->rollBack();
        $message = "Error updating ENUM: " . $e->getMessage();
        $message_type = "danger";
    }
}

// Check for any properties with NULL or invalid property_type
try {
    $query = "SELECT property_id, property_name, property_type FROM properties WHERE property_type IS NULL OR property_type = ''";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $invalid_properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($invalid_properties)) {
        echo "<h3>Properties with Invalid/NULL Property Type:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Property Name</th><th>Current Type</th></tr>";
        foreach ($invalid_properties as $prop) {
            echo "<tr>";
            echo "<td>" . $prop['property_id'] . "</td>";
            echo "<td>" . htmlspecialchars($prop['property_name']) . "</td>";
            echo "<td>" . ($prop['property_type'] ?: 'NULL/Empty') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error checking invalid properties: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Property Types</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>🔧 Update Property Types Database Schema</h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <p>This tool will update the database schema to include "Electronics" as a valid property type.</p>
                        
                        <div class="alert alert-info">
                            <strong>What this does:</strong>
                            <ul>
                                <li>Updates the property_type ENUM to include 'Electronics'</li>
                                <li>Allows saving and displaying Electronics properties correctly</li>
                                <li>Fixes the issue where Electronics doesn't appear in the TYPE column</li>
                            </ul>
                        </div>
                        
                        <?php if (!$has_electronics): ?>
                            <form method="POST" action="">
                                <div class="d-grid gap-2">
                                    <button type="submit" name="update_enum" class="btn btn-primary btn-lg">
                                        🔧 Update Database Schema
                                    </button>
                                </div>
                            </form>
                        <?php else: ?>
                            <div class="alert alert-success">
                                <strong>✅ Database is up to date!</strong> Electronics is already included in the property types.
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-4">
                            <a href="properties.php" class="btn btn-secondary">
                                ← Back to Properties
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
