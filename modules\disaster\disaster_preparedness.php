<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/utility.php';

// Check if user is logged in, if not redirect to login page
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check for database connection errors
$db_error = isset($db_connection_error) && $db_connection_error;

// Initialize variables
$message = '';
$message_type = '';
$page_title = "Disaster Preparedness - Barangay Management System";

// Get disaster data counts
$active_disasters = 0;
$preparedness_count = 0;
$response_count = 0;
$recovery_count = 0;

if (!$db_error) {
    // Count active disasters
    try {
        $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status != 'Completed'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $active_disasters = $row['total'];
        }
        
        // Count by status
        $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status = 'Preparedness'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $preparedness_count = $row['total'];
        }
        
        $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status = 'Response'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $response_count = $row['total'];
        }
        
        $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status = 'Recovery'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $recovery_count = $row['total'];
        }
    } catch (PDOException $e) {
        error_log("Error counting disasters: " . $e->getMessage());
    }
}

// Check if there are messages stored in the session
if (isset($_SESSION['message']) && isset($_SESSION['message_type'])) {
    $message = $_SESSION['message'];
    $message_type = $_SESSION['message_type'];
    // Clear the session messages to prevent them from displaying again
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

// Process form submission for new disaster plan
if (isset($_POST['add_disaster'])) {
    $disaster_type = $_POST['disaster_type'];
    $disaster_date = $_POST['disaster_date'];
    $description = $_POST['description'];
    $affected_areas = $_POST['affected_areas'];
    $affected_households = $_POST['affected_households'];
    $affected_individuals = $_POST['affected_individuals'];
    $evacuees = $_POST['evacuees'];
    $casualties = $_POST['casualties'];
    $status = $_POST['status'];
    $actions_taken = $_POST['actions_taken'];
    $resources_deployed = $_POST['resources_deployed'];
    $external_assistance = $_POST['external_assistance'];
    $recorded_by = $_SESSION['user_id'];
    
    if (!$db_error) {
        $query = "INSERT INTO disaster_management (disaster_type, disaster_date, description, affected_areas, affected_households, affected_individuals, evacuees, casualties, status, 
                  actions_taken, resources_deployed, external_assistance, recorded_by)
                  VALUES (:disaster_type, :disaster_date, :description, :affected_areas, :affected_households, :affected_individuals, :evacuees, :casualties, :status, 
                  :actions_taken, :resources_deployed, :external_assistance, :recorded_by)";
        
        try {
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':disaster_type', $disaster_type);
            $stmt->bindParam(':disaster_date', $disaster_date);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':affected_areas', $affected_areas);
            $stmt->bindParam(':affected_households', $affected_households);
            $stmt->bindParam(':affected_individuals', $affected_individuals);
            $stmt->bindParam(':evacuees', $evacuees);
            $stmt->bindParam(':casualties', $casualties);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':actions_taken', $actions_taken);
            $stmt->bindParam(':resources_deployed', $resources_deployed);
            $stmt->bindParam(':external_assistance', $external_assistance);
            $stmt->bindParam(':recorded_by', $recorded_by);
            $stmt->execute();
            
            // Log the activity
            $action_type = "Disaster Management";
            $action_details = "Added new disaster plan/response for $disaster_type";
            log_activity($conn, $recorded_by, $action_type, $action_details, 'disaster');
            
            $message = "Disaster plan/response has been successfully recorded.";
            $message_type = "success";
            
            // Redirect to prevent form resubmission on page refresh
            $_SESSION['message'] = $message;
            $_SESSION['message_type'] = $message_type;
            header("Location: " . $_SERVER['PHP_SELF']);
            exit();
        } catch(PDOException $e) {
            $message = "Error: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Handle AJAX status update
if (isset($_POST['ajax_update_status']) && isset($_POST['disaster_id']) && isset($_POST['status'])) {
    header('Content-Type: application/json');

    $disaster_id = $_POST['disaster_id'];
    $new_status = $_POST['status'];

    if (!$db_error) {
        $query = "UPDATE disaster_management SET status = :new_status WHERE disaster_id = :disaster_id";

        try {
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':new_status', $new_status);
            $stmt->bindParam(':disaster_id', $disaster_id);
            $stmt->execute();

            // Log the activity
            $user_id = $_SESSION['user_id'];
            $action_type = "Status Update";
            $action_details = "Updated disaster #$disaster_id status to $new_status";
            log_activity($conn, $user_id, $action_type, $action_details, 'disaster');

            echo json_encode([
                'success' => true,
                'message' => "Disaster status has been updated to $new_status.",
                'new_status' => $new_status
            ]);
            exit();
        } catch(PDOException $e) {
            echo json_encode([
                'success' => false,
                'message' => "Error: " . $e->getMessage()
            ]);
            exit();
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => "Database connection error."
        ]);
        exit();
    }
}

// Handle AJAX statistics request
if (isset($_POST['ajax_get_stats'])) {
    header('Content-Type: application/json');

    if (!$db_error) {
        try {
            // Count active disasters
            $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status != 'Completed'";
            $stmt = $conn->query($query);
            $active_disasters = $stmt ? $stmt->fetch(PDO::FETCH_ASSOC)['total'] : 0;

            // Count by status
            $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status = 'Preparedness'";
            $stmt = $conn->query($query);
            $preparedness_count = $stmt ? $stmt->fetch(PDO::FETCH_ASSOC)['total'] : 0;

            $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status = 'Response'";
            $stmt = $conn->query($query);
            $response_count = $stmt ? $stmt->fetch(PDO::FETCH_ASSOC)['total'] : 0;

            $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status = 'Recovery'";
            $stmt = $conn->query($query);
            $recovery_count = $stmt ? $stmt->fetch(PDO::FETCH_ASSOC)['total'] : 0;

            echo json_encode([
                'success' => true,
                'active_disasters' => $active_disasters,
                'preparedness_count' => $preparedness_count,
                'response_count' => $response_count,
                'recovery_count' => $recovery_count
            ]);
            exit();
        } catch(PDOException $e) {
            echo json_encode([
                'success' => false,
                'message' => "Error: " . $e->getMessage()
            ]);
            exit();
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => "Database connection error."
        ]);
        exit();
    }
}

// Update disaster status (legacy support)
if (isset($_GET['update_status']) && !empty($_GET['update_status']) && isset($_GET['status'])) {
    $disaster_id = $_GET['update_status'];
    $new_status = $_GET['status'];

    if (!$db_error) {
        $query = "UPDATE disaster_management SET status = :new_status WHERE disaster_id = :disaster_id";

        try {
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':new_status', $new_status);
            $stmt->bindParam(':disaster_id', $disaster_id);
            $stmt->execute();

            // Log the activity
            $user_id = $_SESSION['user_id'];
            $action_type = "Status Update";
            $action_details = "Updated disaster #$disaster_id status to $new_status";
            log_activity($conn, $user_id, $action_type, $action_details, 'disaster');

            $message = "Disaster status has been updated to $new_status.";
            $message_type = "success";

            // Redirect to prevent query string parameters being retained
            $_SESSION['message'] = $message;
            $_SESSION['message_type'] = $message_type;
            header("Location: " . strtok($_SERVER['REQUEST_URI'], '?'));
            exit();
        } catch(PDOException $e) {
            $message = "Error: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Process form submission for editing disaster plan
if (isset($_POST['edit_disaster'])) {
    $disaster_id = $_POST['disaster_id'];
    $disaster_type = $_POST['disaster_type'];
    $disaster_date = $_POST['disaster_date'];
    $description = $_POST['description'];
    $affected_areas = $_POST['affected_areas'];
    $affected_households = $_POST['affected_households'];
    $affected_individuals = $_POST['affected_individuals'];
    $evacuees = $_POST['evacuees'];
    $casualties = $_POST['casualties'];
    $status = $_POST['status'];
    $actions_taken = $_POST['actions_taken'];
    $resources_deployed = $_POST['resources_deployed'];
    $external_assistance = $_POST['external_assistance'];
    
    if (!$db_error) {
        $query = "UPDATE disaster_management SET 
                  disaster_type = :disaster_type, 
                  disaster_date = :disaster_date, 
                  description = :description, 
                  affected_areas = :affected_areas, 
                  affected_households = :affected_households,
                  affected_individuals = :affected_individuals,
                  evacuees = :evacuees,
                  casualties = :casualties,
                  status = :status, 
                  actions_taken = :actions_taken, 
                  resources_deployed = :resources_deployed,
                  external_assistance = :external_assistance
                  WHERE disaster_id = :disaster_id";
        
        try {
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':disaster_type', $disaster_type);
            $stmt->bindParam(':disaster_date', $disaster_date);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':affected_areas', $affected_areas);
            $stmt->bindParam(':affected_households', $affected_households);
            $stmt->bindParam(':affected_individuals', $affected_individuals);
            $stmt->bindParam(':evacuees', $evacuees);
            $stmt->bindParam(':casualties', $casualties);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':actions_taken', $actions_taken);
            $stmt->bindParam(':resources_deployed', $resources_deployed);
            $stmt->bindParam(':external_assistance', $external_assistance);
            $stmt->bindParam(':disaster_id', $disaster_id);
            $stmt->execute();
            
            // Log the activity
            $user_id = $_SESSION['user_id'];
            $action_type = "Disaster Management";
            $action_details = "Updated disaster plan/response #$disaster_id: $disaster_type";
            log_activity($conn, $user_id, $action_type, $action_details, 'disaster');
            
            $message = "Disaster plan/response has been successfully updated.";
            $message_type = "success";
            
            // Redirect to prevent form resubmission on page refresh
            $_SESSION['message'] = $message;
            $_SESSION['message_type'] = $message_type;
            header("Location: " . $_SERVER['PHP_SELF']);
            exit();
        } catch(PDOException $e) {
            $message = "Error: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Handle delete disaster
if (isset($_POST['delete_disaster'])) {
    try {
        $disaster_id = (int)$_POST['disaster_id'];

        // Get disaster details for logging before deletion
        $disaster_query = "SELECT * FROM disaster_management WHERE disaster_id = ?";
        $disaster_stmt = $conn->prepare($disaster_query);
        $disaster_stmt->execute([$disaster_id]);
        $disaster_info = $disaster_stmt->fetch(PDO::FETCH_ASSOC);

        if ($disaster_info) {
            // Delete the disaster record
            $delete_query = "DELETE FROM disaster_management WHERE disaster_id = ?";
            $delete_stmt = $conn->prepare($delete_query);
            $delete_stmt->execute([$disaster_id]);

            // Log the activity
            $user_id = $_SESSION['user_id'];
            $action_type = "Disaster Management";
            $action_details = "Deleted disaster record #$disaster_id: {$disaster_info['disaster_type']} on " . date('M d, Y', strtotime($disaster_info['disaster_date']));
            log_activity($conn, $user_id, $action_type, $action_details, 'disaster', $disaster_id);

            $message = "Disaster record has been successfully deleted.";
            $message_type = "success";
        } else {
            $message = "Disaster record not found.";
            $message_type = "warning";
        }

        // Redirect to prevent form resubmission
        $_SESSION['message'] = $message;
        $_SESSION['message_type'] = $message_type;
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch (PDOException $e) {
        $message = "Error deleting disaster record: " . $e->getMessage();
        $message_type = "danger";
    }
}

// Get all disaster records
$disasters = array();
if (!$db_error) {
    try {
        $query = "SELECT d.*, u.username as recorder_name
                FROM disaster_management d
                LEFT JOIN users u ON d.recorded_by = u.user_id
                ORDER BY d.date_recorded DESC";
        
        $stmt = $conn->query($query);
        if ($stmt) {
            $disasters = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch(PDOException $e) {
        error_log("Error retrieving disasters: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Custom styles for disaster management table */
        @media (max-width: 767px) {
            .d-flex.gap-1 {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .table-responsive {
                overflow-x: auto;
            }
            
            .modal-dialog {
                margin: 0.5rem;
            }
            
            .badge {
                display: inline-block;
                width: 100%;
                text-align: center;
            }
        }
        
        /* Make action buttons more visible */
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        
        /* Improve tooltip appearance */
        .tooltip-inner {
            max-width: 300px;
            padding: 0.5rem;
            background-color: rgba(0, 0, 0, 0.9);
            border-radius: 0.25rem;
            text-align: left;
        }
        
        /* Make table cells more readable */
        #disasterTable td {
            vertical-align: middle;
        }
        
        /* Custom styling for status badges */
        .badge {
            font-size: 0.85rem;
            padding: 0.35rem 0.5rem;
        }
        
        /* Enhanced modal styling */
        .disaster-info {
            border-left: 3px solid #4e73df;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        
        .disaster-info strong {
            color: #4e73df;
        }
        
        .detail-section {
            background-color: #f8f9fc;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .detail-section h6 {
            border-bottom: 1px solid #e3e6f0;
            padding-bottom: 10px;
            margin-bottom: 15px;
            color: #4e73df;
        }
        
        .detail-content {
            padding: 0 10px;
        }
        
        .emoji-label {
            margin-right: 10px;
            font-size: 1.3rem;
        }
        
        .info-label {
            font-weight: 600;
            color: #5a5c69;
        }
        
        /* Form enhancement */
        .form-label {
            font-weight: 500;
            color: #5a5c69;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #bac8f3;
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        }
        
        .section-divider {
            height: 1px;
            background-color: #e3e6f0;
            margin: 25px 0;
        }
        
        /* Stats Card Styling */
        .stat-card {
            border-radius: 0.75rem;
            overflow: hidden;
            height: 100%;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            position: relative;
            z-index: 1;
        }
        
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
            cursor: pointer;
        }
        
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        
        .border-primary { border-left: 4px solid #4e73df !important; }
        .border-success { border-left: 4px solid #1cc88a !important; }
        .border-info { border-left: 4px solid #36b9cc !important; }
        .border-warning { border-left: 4px solid #f6c23e !important; }
        .border-danger { border-left: 4px solid #e74a3b !important; }
        
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
        
        .content-card {
            border-radius: 15px;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .content-card:hover {
            transform: translateY(-7px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
        }
        
        /* Toast notification styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        
        .toast {
            background-color: white;
            width: 350px;
            max-width: 100%;
            font-size: 0.875rem;
            pointer-events: auto;
            background-clip: padding-box;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.25rem;
            opacity: 0;
            transition: transform .15s ease-in-out, opacity .15s ease-in-out;
            transform: translateY(-100%);
        }
        
        .toast.showing {
            opacity: 1;
            transform: translateY(0);
        }
        
        .toast.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        
        .toast.hide {
            display: none;
        }
        
        .toast-header {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            color: #6c757d;
            background-color: rgba(255, 255, 255, 0.85);
            background-clip: padding-box;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            border-top-left-radius: calc(0.25rem - 1px);
            border-top-right-radius: calc(0.25rem - 1px);
        }
        
        .toast-body {
            padding: 0.75rem;
        }
        
        .me-auto {
            margin-right: auto !important;
        }

        /* Enhanced dropdown styling for status changes */
        .dropdown-menu {
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
            animation: fadeInDown 0.3s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dropdown-item {
            transition: all 0.2s ease;
            border-radius: 0.25rem;
            margin: 0.1rem 0.5rem;
        }

        .dropdown-item:hover {
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateX(5px);
        }

        .status-item:hover .status-icon {
            transform: scale(1.2);
            transition: transform 0.2s ease;
        }

        /* Loading state for buttons */
        .btn-loading {
            position: relative;
            pointer-events: none;
        }

        .btn-loading::after {
            content: "";
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Improved table row highlighting */
        #disasterTable tbody tr {
            transition: background-color 0.2s ease;
        }

        #disasterTable tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* Status badge animations */
        .badge {
            transition: all 0.3s ease;
        }

        .badge.updating {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🛡️ Disaster Preparedness</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDisasterModal">
                            <i class="fas fa-plus"></i> New Disaster Plan
                        </button>
                    </div>
                </div>

                <?php if ($db_error): ?>
                <div class="alert alert-danger">
                    <strong>Database Error:</strong> Could not connect to the database. Please contact the system administrator.
                </div>
                <?php else: ?>

                <!-- Display Messages -->
                <?php /* if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; */ ?>

                <!-- Disaster Statistics Cards -->
                <div class="row mt-4">
                    <!-- Active Disasters -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-danger">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-danger-soft text-danger">
                                            🚨
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($active_disasters); ?></h4>
                                        <p class="mb-0 text-muted">Active Disasters</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preparedness Plans -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-warning">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-warning-soft text-warning">
                                            ⚠️
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($preparedness_count); ?></h4>
                                        <p class="mb-0 text-muted">Preparedness Plans</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Response Operations -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-primary">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-primary-soft text-primary">
                                            🛠️
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($response_count); ?></h4>
                                        <p class="mb-0 text-muted">Response Operations</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recovery Projects -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-success">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-success-soft text-success">
                                            🔄
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($recovery_count); ?></h4>
                                        <p class="mb-0 text-muted">Recovery Projects</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Disaster Management Table -->
                <div class="card shadow mb-4 content-card">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">🌪️ Disaster Management Plans & Activities</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover table-bordered-columns" id="disasterTable" width="100%" cellspacing="0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">🔢 ID</th>
                                        <th width="10%">🌪️ Disaster Type</th>
                                        <th width="8%">📅 Date</th>
                                        <th width="15%">📝 Description</th>
                                        <th width="10%">🌍 Affected Areas</th>
                                        <th width="7%">🏠 Households</th>
                                        <th width="7%">👥 Individuals</th>
                                        <th width="5%">⛺ Evacuees</th>
                                        <th width="5%">⚠️ Casualties</th>
                                        <th width="8%">🚦 Status</th>
                                        <th width="10%">🛠️ Actions Taken</th>
                                        <th width="10%">🤝 External Help</th>
                                        <th width="10%">⚙️ Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($disasters as $disaster): ?>
                                    <tr>
                                        <td><?php echo $disaster['disaster_id']; ?></td>
                                        <td><?php echo htmlspecialchars($disaster['disaster_type']); ?></td>
                                        <td><?php echo isset($disaster['disaster_date']) ? date('M d, Y', strtotime($disaster['disaster_date'])) : 'N/A'; ?></td>
                                        <td data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($disaster['description']); ?>">
                                            <?php echo mb_substr(htmlspecialchars($disaster['description']), 0, 50) . (strlen($disaster['description']) > 50 ? '...' : ''); ?>
                                        </td>
                                        <td data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($disaster['affected_areas']); ?>">
                                            <?php echo mb_substr(htmlspecialchars($disaster['affected_areas']), 0, 30) . (strlen($disaster['affected_areas']) > 30 ? '...' : ''); ?>
                                        </td>
                                        <td><?php echo number_format($disaster['affected_households'] ?? 0); ?></td>
                                        <td><?php echo number_format($disaster['affected_individuals'] ?? 0); ?></td>
                                        <td><?php echo number_format($disaster['evacuees'] ?? 0); ?></td>
                                        <td><?php echo number_format($disaster['casualties'] ?? 0); ?></td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo ($disaster['status'] == 'Preparedness') ? 'warning' : 
                                                    (($disaster['status'] == 'Response') ? 'danger' : 
                                                    (($disaster['status'] == 'Recovery') ? 'primary' : 
                                                    (($disaster['status'] == 'Completed') ? 'success' : 'secondary'))); 
                                            ?>">
                                                <?php echo $disaster['status']; ?>
                                            </span>
                                        </td>
                                        <td data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($disaster['actions_taken']); ?>">
                                            <?php echo mb_substr(htmlspecialchars($disaster['actions_taken']), 0, 50) . (strlen($disaster['actions_taken']) > 50 ? '...' : ''); ?>
                                        </td>
                                        <td data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($disaster['external_assistance'] ?? ''); ?>">
                                            <?php echo mb_substr(htmlspecialchars($disaster['external_assistance'] ?? ''), 0, 50) . (strlen($disaster['external_assistance'] ?? '') > 50 ? '...' : ''); ?>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1 justify-content-center">
                                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewDisasterModal<?php echo $disaster['disaster_id']; ?>" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editDisasterModal<?php echo $disaster['disaster_id']; ?>" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <div class="btn-group">
                                                    <button type="button" class="btn btn-sm btn-warning dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="Change Status">
                                                        <i class="fas fa-flag"></i>
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0 py-0 overflow-hidden" style="min-width: 200px;">
                                                        <li class="dropdown-header bg-light fw-bold py-2 text-dark border-bottom">
                                                            <i class="fas fa-exchange-alt me-2"></i>Change Status
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item py-2 d-flex align-items-center status-item text-warning" href="#" 
                                                               data-id="<?php echo $disaster['disaster_id']; ?>" 
                                                               data-status="Preparedness">
                                                                <span class="status-icon me-2">⚠️</span>
                                                                <span>Preparedness</span>
                                                                <span class="ms-auto">
                                                                    <span class="badge bg-warning text-dark">Planning</span>
                                                                </span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item py-2 d-flex align-items-center status-item text-danger" href="#" 
                                                               data-id="<?php echo $disaster['disaster_id']; ?>" 
                                                               data-status="Response">
                                                                <span class="status-icon me-2">🚨</span>
                                                                <span>Response</span>
                                                                <span class="ms-auto">
                                                                    <span class="badge bg-danger">Active</span>
                                                                </span>
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item py-2 d-flex align-items-center status-item text-primary" href="#" 
                                                               data-id="<?php echo $disaster['disaster_id']; ?>" 
                                                               data-status="Recovery">
                                                                <span class="status-icon me-2">🔄</span>
                                                                <span>Recovery</span>
                                                                <span class="ms-auto">
                                                                    <span class="badge bg-primary">In Progress</span>
                                                                </span>
                                                            </a>
                                                        </li>
                                                        <li><hr class="dropdown-divider m-0"></li>
                                                        <li>
                                                            <a class="dropdown-item py-2 d-flex align-items-center status-item text-success" href="#" 
                                                               data-id="<?php echo $disaster['disaster_id']; ?>" 
                                                               data-status="Completed">
                                                                <span class="status-icon me-2">✅</span>
                                                                <span>Completed</span>
                                                                <span class="ms-auto">
                                                                    <span class="badge bg-success">Done</span>
                                                                </span>
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <button type="button" class="btn btn-sm btn-danger delete-disaster" data-id="<?php echo $disaster['disaster_id']; ?>" data-type="<?php echo htmlspecialchars($disaster['disaster_type']); ?>" data-date="<?php echo date('M d, Y', strtotime($disaster['disaster_date'])); ?>" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Add Disaster Modal -->
    <div class="modal fade" id="addDisasterModal" tabindex="-1" aria-labelledby="addDisasterModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addDisasterModalLabel"><i class="fas fa-plus me-2"></i>Add New Disaster Plan/Response</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="disaster_type" class="form-label">
                                        <span class="emoji-label">🌪️</span>Disaster Type
                                    </label>
                                    <input type="text" class="form-control" id="disaster_type" name="disaster_type" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="disaster_date" class="form-label">
                                        <span class="emoji-label">📅</span>Disaster Date
                                    </label>
                                    <input type="date" class="form-control" id="disaster_date" name="disaster_date" value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">
                                        <span class="emoji-label">🚨</span>Status
                                    </label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="Preparedness">Preparedness</option>
                                        <option value="Response">Response</option>
                                        <option value="Recovery">Recovery</option>
                                        <option value="Completed">Completed</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="affected_areas" class="form-label">
                                        <span class="emoji-label">🌍</span>Affected Areas
                                    </label>
                                    <input type="text" class="form-control" id="affected_areas" name="affected_areas" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    <span class="emoji-label">📝</span>Description
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                            </div>
                        </div>
                        
                        <div class="section-divider"></div>
                        
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-chart-bar me-2"></i>Impact Statistics</h6>
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="affected_households" class="form-label">
                                        <span class="emoji-label">🏠</span>Households
                                    </label>
                                    <input type="number" class="form-control" id="affected_households" name="affected_households" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="affected_individuals" class="form-label">
                                        <span class="emoji-label">👥</span>Individuals
                                    </label>
                                    <input type="number" class="form-control" id="affected_individuals" name="affected_individuals" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="evacuees" class="form-label">
                                        <span class="emoji-label">🏕️</span>Evacuees
                                    </label>
                                    <input type="number" class="form-control" id="evacuees" name="evacuees" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="casualties" class="form-label">
                                        <span class="emoji-label">💔</span>Casualties
                                    </label>
                                    <input type="number" class="form-control" id="casualties" name="casualties" min="0" value="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="section-divider"></div>
                        
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-hands-helping me-2"></i>Response & Assistance</h6>
                            <div class="mb-3">
                                <label for="actions_taken" class="form-label">
                                    <span class="emoji-label">🛠️</span>Actions Taken
                                </label>
                                <textarea class="form-control" id="actions_taken" name="actions_taken" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="resources_deployed" class="form-label">
                                    <span class="emoji-label">🚒</span>Resources Deployed
                                </label>
                                <textarea class="form-control" id="resources_deployed" name="resources_deployed" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="external_assistance" class="form-label">
                                    <span class="emoji-label">🤝</span>External Assistance
                                </label>
                                <textarea class="form-control" id="external_assistance" name="external_assistance" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_disaster" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View/Edit Disaster Modals - These would be generated for each disaster -->
    <?php foreach ($disasters as $disaster): ?>
    <!-- View Disaster Modal -->
    <div class="modal fade" id="viewDisasterModal<?php echo $disaster['disaster_id']; ?>" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>Disaster Details</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="detail-section">
                                <h6 class="fw-bold"><i class="fas fa-clipboard me-2"></i>Basic Information</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><span class="emoji-label">🆔</span><span class="info-label">ID:</span> <?php echo $disaster['disaster_id']; ?></p>
                                        <p><span class="emoji-label">🌪️</span><span class="info-label">Disaster Type:</span> <?php echo htmlspecialchars($disaster['disaster_type']); ?></p>
                                        <p><span class="emoji-label">📅</span><span class="info-label">Disaster Date:</span> <?php echo isset($disaster['disaster_date']) ? date('M d, Y', strtotime($disaster['disaster_date'])) : 'Not specified'; ?></p>
                                        <p>
                                            <span class="emoji-label">🚨</span><span class="info-label">Status:</span> 
                                            <span class="badge bg-<?php 
                                                echo ($disaster['status'] == 'Preparedness') ? 'warning' : 
                                                    (($disaster['status'] == 'Response') ? 'danger' : 
                                                    (($disaster['status'] == 'Recovery') ? 'primary' : 
                                                    (($disaster['status'] == 'Completed') ? 'success' : 'secondary'))); 
                                            ?>"><?php echo $disaster['status']; ?></span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><span class="emoji-label">👤</span><span class="info-label">Recorded By:</span> <?php echo htmlspecialchars($disaster['recorder_name']); ?></p>
                                        <p><span class="emoji-label">⏱️</span><span class="info-label">Date Recorded:</span> <?php echo date('M d, Y h:i A', strtotime($disaster['date_recorded'])); ?></p>
                                        <p><span class="emoji-label">🌍</span><span class="info-label">Affected Areas:</span> <?php echo htmlspecialchars($disaster['affected_areas']); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="detail-section">
                                <h6 class="fw-bold"><i class="fas fa-chart-bar me-2"></i>Impact Statistics</h6>
                                <div class="row">
                                    <div class="col-md-3 col-6 mb-3">
                                        <div class="text-center">
                                            <div class="emoji-label fs-2">🏠</div>
                                            <div class="info-label">Households</div>
                                            <div class="fs-4"><?php echo number_format($disaster['affected_households'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-6 mb-3">
                                        <div class="text-center">
                                            <div class="emoji-label fs-2">👥</div>
                                            <div class="info-label">Individuals</div>
                                            <div class="fs-4"><?php echo number_format($disaster['affected_individuals'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <div class="text-center">
                                            <div class="emoji-label fs-2">🏕️</div>
                                            <div class="info-label">Evacuees</div>
                                            <div class="fs-4"><?php echo number_format($disaster['evacuees'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <div class="text-center">
                                            <div class="emoji-label fs-2">💔</div>
                                            <div class="info-label">Casualties</div>
                                            <div class="fs-4"><?php echo number_format($disaster['casualties'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="detail-section">
                                <h6 class="fw-bold"><i class="fas fa-file-alt me-2"></i>Details & Actions</h6>
                                <div class="detail-content mb-3">
                                    <p><span class="emoji-label">📝</span><span class="info-label">Description:</span></p>
                                    <p class="ps-4 bg-light p-2 rounded"><?php echo nl2br(htmlspecialchars($disaster['description'])); ?></p>
                                </div>
                                
                                <div class="detail-content mb-3">
                                    <p><span class="emoji-label">🛠️</span><span class="info-label">Actions Taken:</span></p>
                                    <p class="ps-4 bg-light p-2 rounded"><?php echo nl2br(htmlspecialchars($disaster['actions_taken'])); ?></p>
                                </div>
                                
                                <div class="detail-content mb-3">
                                    <p><span class="emoji-label">🚒</span><span class="info-label">Resources Deployed:</span></p>
                                    <p class="ps-4 bg-light p-2 rounded"><?php echo nl2br(htmlspecialchars($disaster['resources_deployed'])); ?></p>
                                </div>
                                
                                <div class="detail-content">
                                    <p><span class="emoji-label">🤝</span><span class="info-label">External Assistance:</span></p>
                                    <p class="ps-4 bg-light p-2 rounded"><?php echo !empty($disaster['external_assistance']) ? nl2br(htmlspecialchars($disaster['external_assistance'])) : 'None'; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editDisasterModal<?php echo $disaster['disaster_id']; ?>">
                        <i class="fas fa-edit me-1"></i>Edit
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Disaster Modal -->
    <div class="modal fade" id="editDisasterModal<?php echo $disaster['disaster_id']; ?>" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Disaster Plan/Response</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="" method="post">
                    <input type="hidden" name="disaster_id" value="<?php echo $disaster['disaster_id']; ?>">
                    <div class="modal-body">
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="edit_disaster_type<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🌪️</span>Disaster Type
                                    </label>
                                    <input type="text" class="form-control" id="edit_disaster_type<?php echo $disaster['disaster_id']; ?>" name="disaster_type" value="<?php echo htmlspecialchars($disaster['disaster_type']); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="edit_disaster_date<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">📅</span>Disaster Date
                                    </label>
                                    <input type="date" class="form-control" id="edit_disaster_date<?php echo $disaster['disaster_id']; ?>" name="disaster_date" value="<?php echo date('Y-m-d', strtotime($disaster['disaster_date'] ?? date('Y-m-d'))); ?>" required>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="edit_status<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🚨</span>Status
                                    </label>
                                    <select class="form-select" id="edit_status<?php echo $disaster['disaster_id']; ?>" name="status" required>
                                        <option value="Preparedness" <?php echo ($disaster['status'] == 'Preparedness') ? 'selected' : ''; ?>>Preparedness</option>
                                        <option value="Response" <?php echo ($disaster['status'] == 'Response') ? 'selected' : ''; ?>>Response</option>
                                        <option value="Recovery" <?php echo ($disaster['status'] == 'Recovery') ? 'selected' : ''; ?>>Recovery</option>
                                        <option value="Completed" <?php echo ($disaster['status'] == 'Completed') ? 'selected' : ''; ?>>Completed</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="edit_affected_areas<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🌍</span>Affected Areas
                                    </label>
                                    <input type="text" class="form-control" id="edit_affected_areas<?php echo $disaster['disaster_id']; ?>" name="affected_areas" value="<?php echo htmlspecialchars($disaster['affected_areas']); ?>" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="edit_description<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                    <span class="emoji-label">📝</span>Description
                                </label>
                                <textarea class="form-control" id="edit_description<?php echo $disaster['disaster_id']; ?>" name="description" rows="3" required><?php echo htmlspecialchars($disaster['description']); ?></textarea>
                            </div>
                        </div>
                        
                        <div class="section-divider"></div>
                        
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-chart-bar me-2"></i>Impact Statistics</h6>
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="edit_affected_households<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🏠</span>Households
                                    </label>
                                    <input type="number" class="form-control" id="edit_affected_households<?php echo $disaster['disaster_id']; ?>" name="affected_households" min="0" value="<?php echo $disaster['affected_households'] ?? 0; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="edit_affected_individuals<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">👥</span>Individuals
                                    </label>
                                    <input type="number" class="form-control" id="edit_affected_individuals<?php echo $disaster['disaster_id']; ?>" name="affected_individuals" min="0" value="<?php echo $disaster['affected_individuals'] ?? 0; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="edit_evacuees<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🏕️</span>Evacuees
                                    </label>
                                    <input type="number" class="form-control" id="edit_evacuees<?php echo $disaster['disaster_id']; ?>" name="evacuees" min="0" value="<?php echo $disaster['evacuees'] ?? 0; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="edit_casualties<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">💔</span>Casualties
                                    </label>
                                    <input type="number" class="form-control" id="edit_casualties<?php echo $disaster['disaster_id']; ?>" name="casualties" min="0" value="<?php echo $disaster['casualties'] ?? 0; ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="section-divider"></div>
                        
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-hands-helping me-2"></i>Response & Assistance</h6>
                            <div class="mb-3">
                                <label for="edit_actions_taken<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                    <span class="emoji-label">🛠️</span>Actions Taken
                                </label>
                                <textarea class="form-control" id="edit_actions_taken<?php echo $disaster['disaster_id']; ?>" name="actions_taken" rows="3" required><?php echo htmlspecialchars($disaster['actions_taken']); ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="edit_resources_deployed<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                    <span class="emoji-label">🚒</span>Resources Deployed
                                </label>
                                <textarea class="form-control" id="edit_resources_deployed<?php echo $disaster['disaster_id']; ?>" name="resources_deployed" rows="3" required><?php echo htmlspecialchars($disaster['resources_deployed']); ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="edit_external_assistance<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                    <span class="emoji-label">🤝</span>External Assistance
                                </label>
                                <textarea class="form-control" id="edit_external_assistance<?php echo $disaster['disaster_id']; ?>" name="external_assistance" rows="3"><?php echo htmlspecialchars($disaster['external_assistance'] ?? ''); ?></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="edit_disaster" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; ?>

    <!-- Include jQuery, Bootstrap JS, and DataTables JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            $('#disasterTable').DataTable({
                "order": [[0, "desc"]],
                "responsive": true,
                "columnDefs": [
                    { "width": "5%", "targets": 0 },   // ID column
                    { "width": "10%", "targets": 1 },  // Disaster Type
                    { "width": "8%", "targets": 2 },   // Date
                    { "width": "15%", "targets": 3 },  // Description
                    { "width": "10%", "targets": 4 },  // Affected Areas
                    { "width": "7%", "targets": 5 },   // Households
                    { "width": "7%", "targets": 6 },   // Individuals
                    { "width": "5%", "targets": 7 },   // Evacuees
                    { "width": "5%", "targets": 8 },   // Casualties
                    { "width": "8%", "targets": 9 },   // Status
                    { "width": "10%", "targets": 10 }, // Actions Taken
                    { "width": "10%", "targets": 11 }, // External Assistance
                    { "width": "10%", "targets": 12, "orderable": false }  // Actions column
                ],
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
            
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl, {
                    boundary: document.body
                });
            });
            
            // Status change functionality with confirmation modal and AJAX
            $('.status-item').on('click', function(e) {
                e.preventDefault();

                const disasterId = $(this).data('id');
                const newStatus = $(this).data('status');
                const statusText = $(this).find('span:nth-child(2)').text();
                const statusEmoji = $(this).find('.status-icon').text();

                // Create and show the confirmation modal
                const modalId = 'statusConfirmModal' + Math.floor(Math.random() * 1000);
                const modalHtml = `
                <div class="modal fade" id="${modalId}" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header ${newStatus === 'Completed' ? 'bg-success' : (newStatus === 'Response' ? 'bg-danger' : (newStatus === 'Recovery' ? 'bg-primary' : 'bg-warning'))} text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-exchange-alt me-2"></i>Change Status
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body py-4">
                                <div class="text-center mb-4">
                                    <span style="font-size: 3rem;">${statusEmoji}</span>
                                    <h4 class="mt-3">Update to "${statusText}" Status?</h4>
                                    <p class="text-muted">
                                        This will change the disaster management plan status to ${statusText}.
                                        ${newStatus === 'Completed' ? 'The item will be marked as resolved.' : ''}
                                    </p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn ${newStatus === 'Completed' ? 'btn-success' : (newStatus === 'Response' ? 'btn-danger' : (newStatus === 'Recovery' ? 'btn-primary' : 'btn-warning'))}" onclick="updateDisasterStatus(${disasterId}, '${newStatus}', '${modalId}')">
                                    <i class="fas fa-check-circle me-1"></i>Confirm Change
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                `;

                // Add modal to body and show it
                $('body').append(modalHtml);
                const modal = new bootstrap.Modal(document.getElementById(modalId));
                modal.show();

                // Remove modal from DOM after it's hidden
                $(`#${modalId}`).on('hidden.bs.modal', function() {
                    $(this).remove();
                });
            });

            // Function to update disaster status via AJAX
            window.updateDisasterStatus = function(disasterId, newStatus, modalId) {
                // Show loading state
                const confirmBtn = $(`#${modalId} .modal-footer .btn:last-child`);
                const originalText = confirmBtn.html();
                confirmBtn.html('<i class="fas fa-spinner fa-spin me-1"></i>Updating...').prop('disabled', true);

                // Add visual feedback to the table row
                const tableRow = $(`tr:has(button[data-id="${disasterId}"])`);
                const statusBadge = tableRow.find('.badge');
                statusBadge.addClass('updating');

                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: {
                        ajax_update_status: true,
                        disaster_id: disasterId,
                        status: newStatus
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Hide the modal
                            $(`#${modalId}`).modal('hide');

                            // Update the status badge in the table with animation
                            const badgeClass = newStatus === 'Preparedness' ? 'bg-warning' :
                                             (newStatus === 'Response' ? 'bg-danger' :
                                             (newStatus === 'Recovery' ? 'bg-primary' :
                                             (newStatus === 'Completed' ? 'bg-success' : 'bg-secondary')));

                            // Animate the status change
                            statusBadge.fadeOut(200, function() {
                                $(this).removeClass('bg-warning bg-danger bg-primary bg-success bg-secondary updating')
                                       .addClass(badgeClass)
                                       .text(newStatus)
                                       .fadeIn(200);
                            });

                            // Highlight the row briefly
                            tableRow.addClass('table-success');
                            setTimeout(() => {
                                tableRow.removeClass('table-success');
                            }, 2000);

                            // Show success toast
                            showToast(response.message, 'success');

                            // Update statistics after a short delay
                            setTimeout(() => {
                                updateStatistics();
                            }, 500);
                        } else {
                            // Remove updating class
                            statusBadge.removeClass('updating');

                            // Show error toast
                            showToast(response.message, 'error');

                            // Reset button
                            confirmBtn.html(originalText).prop('disabled', false);
                        }
                    },
                    error: function(xhr, status, error) {
                        // Remove updating class
                        statusBadge.removeClass('updating');

                        // Show error toast
                        showToast('An error occurred while updating the status. Please try again.', 'error');

                        // Reset button
                        confirmBtn.html(originalText).prop('disabled', false);
                    }
                });
            };

            // Function to update statistics without full page reload
            window.updateStatistics = function() {
                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: {
                        ajax_get_stats: true
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Update the statistics cards with animation
                            $('.stat-card').each(function(index) {
                                const card = $(this);
                                const countElement = card.find('h4');
                                const currentCount = parseInt(countElement.text().replace(/,/g, ''));
                                let newCount;

                                switch(index) {
                                    case 0: newCount = response.active_disasters; break;
                                    case 1: newCount = response.preparedness_count; break;
                                    case 2: newCount = response.response_count; break;
                                    case 3: newCount = response.recovery_count; break;
                                }

                                if (currentCount !== newCount) {
                                    // Animate the count change
                                    countElement.fadeOut(200, function() {
                                        $(this).text(newCount.toLocaleString()).fadeIn(200);
                                    });
                                }
                            });
                        }
                    },
                    error: function() {
                        console.log('Failed to update statistics');
                    }
                });
            };

            // Function to show toast notifications
            window.showToast = function(message, type = 'success') {
                const toastId = 'toast_' + Date.now();
                const iconMap = {
                    'success': '✅',
                    'error': '❌',
                    'warning': '⚠️',
                    'info': 'ℹ️'
                };

                const colorMap = {
                    'success': '#28a745',
                    'error': '#dc3545',
                    'warning': '#ffc107',
                    'info': '#17a2b8'
                };

                const toastHtml = `
                <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" style="background-color: ${colorMap[type]}; color: ${type === 'warning' ? '#000' : 'white'};">
                    <div class="d-flex align-items-center p-3">
                        <div class="me-2">${iconMap[type]}</div>
                        <div class="me-auto">${message}</div>
                        <div>
                            <button type="button" class="btn-close ${type === 'warning' ? '' : 'btn-close-white'}" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                </div>
                `;

                $('.toast-container').append(toastHtml);
                const toast = new bootstrap.Toast(document.getElementById(toastId), {
                    delay: 5000
                });
                toast.show();

                // Remove toast from DOM after it's hidden
                $(`#${toastId}`).on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            };
            
            // Show toast notification if there's a message
            <?php if(!empty($message)): ?>
            // Update toast styling based on message type
            const toast = document.getElementById('successToast');
            
            <?php if($message_type == 'success'): ?>
            toast.style.backgroundColor = '#28a745';
            <?php elseif($message_type == 'danger' || $message_type == 'error'): ?>
            toast.style.backgroundColor = '#dc3545';
            <?php elseif($message_type == 'warning'): ?>
            toast.style.backgroundColor = '#ffc107';
            toast.style.color = '#000';
            <?php else: ?>
            toast.style.backgroundColor = '#17a2b8';
            <?php endif; ?>
            
            toast.style.color = <?php echo ($message_type == 'warning') ? "'#000'" : "'white'"; ?>;
            
            const successToast = new bootstrap.Toast(toast, {
                delay: 5000
            });
            successToast.show();
            <?php endif; ?>
        });
    </script>
    
    <!-- Toast container -->
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="me-2">✅</div>
                <div class="me-auto"><?php echo $message; ?></div>
                <div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteDisasterModal" tabindex="-1" aria-labelledby="deleteDisasterModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteDisasterModalLabel">🗑️ Delete Disaster Record</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Warning!</strong> This action cannot be undone.
                    </div>
                    <p>Are you sure you want to delete this disaster record?</p>
                    <div class="disaster-info">
                        <strong>Disaster Type:</strong> <span id="delete_disaster_type"></span><br>
                        <strong>Date:</strong> <span id="delete_disaster_date"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">❌ Cancel</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="disaster_id" id="delete_disaster_id">
                        <button type="submit" name="delete_disaster" class="btn btn-danger">
                            🗑️ Delete Record
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Handle delete disaster button clicks
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('.delete-disaster');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const disasterId = this.getAttribute('data-id');
                    const disasterType = this.getAttribute('data-type');
                    const disasterDate = this.getAttribute('data-date');

                    document.getElementById('delete_disaster_id').value = disasterId;
                    document.getElementById('delete_disaster_type').textContent = disasterType;
                    document.getElementById('delete_disaster_date').textContent = disasterDate;

                    const modal = new bootstrap.Modal(document.getElementById('deleteDisasterModal'));
                    modal.show();
                });
            });
        });
    </script>
</body>
</html>