<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/utility.php';

// Check if user is logged in, if not redirect to login page
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check for database connection errors
$db_error = isset($db_connection_error) && $db_connection_error;

// Initialize variables
$message = '';
$message_type = '';
$page_title = "Edit Assistance Program - Barangay Management System";

// Check if there are messages stored in the session
if (isset($_SESSION['message']) && isset($_SESSION['message_type'])) {
    $message = $_SESSION['message'];
    $message_type = $_SESSION['message_type'];
    // Clear the session messages to prevent them from displaying again
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

// Get program ID from URL
$program_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($program_id <= 0) {
    $_SESSION['message'] = "Invalid program ID.";
    $_SESSION['message_type'] = "danger";
    header("Location: programs.php");
    exit;
}

// Process Edit Program Form
if (isset($_POST['edit_program'])) {
    $program_name = $_POST['program_name'] ?? '';
    $program_type = $_POST['program_type'] ?? '';
    $description = $_POST['description'] ?? '';
    $budget = $_POST['budget'] ?? null;
    $start_date = $_POST['start_date'] ?? null;
    $end_date = $_POST['end_date'] ?? null;
    $beneficiary_criteria = $_POST['beneficiary_criteria'] ?? '';
    $status = $_POST['status'] ?? 'Active';
    $coordinator_id = !empty($_POST['coordinator_id']) ? $_POST['coordinator_id'] : null;

    if (empty($budget)) $budget = null;
    if (empty($start_date)) $start_date = null;
    if (empty($end_date)) $end_date = null;

    if (!$db_error) {
        try {
            // Update the program
            $query = "UPDATE assistance_programs SET 
                    program_name = :program_name, 
                    program_type = :program_type, 
                    description = :description, 
                    budget = :budget, 
                    start_date = :start_date, 
                    end_date = :end_date, 
                    beneficiary_criteria = :beneficiary_criteria, 
                    status = :status, 
                    coordinator_id = :coordinator_id 
                    WHERE program_id = :program_id";
            
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':program_name', $program_name);
            $stmt->bindParam(':program_type', $program_type);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':budget', $budget);
            $stmt->bindParam(':start_date', $start_date);
            $stmt->bindParam(':end_date', $end_date);
            $stmt->bindParam(':beneficiary_criteria', $beneficiary_criteria);
            $stmt->bindParam(':status', $status);
            // Fix for foreign key constraint - use PDO::PARAM_NULL when coordinator_id is null
            if ($coordinator_id === null) {
                $stmt->bindValue(':coordinator_id', null, PDO::PARAM_NULL);
            } else {
                $stmt->bindParam(':coordinator_id', $coordinator_id);
            }
            $stmt->bindParam(':program_id', $program_id);
            $stmt->execute();
            
            // Log the activity
            $action_type = "Assistance";
            $action_details = "Updated assistance program: $program_name (ID: $program_id)";
            log_activity($conn, $_SESSION['user_id'], $action_type, $action_details, 'assistance');
            
            $message = "Assistance program has been successfully updated.";
            $message_type = "success";
            
            // Store messages in session and redirect
            $_SESSION['message'] = $message;
            $_SESSION['message_type'] = $message_type;
            header("Location: programs.php");
            exit();
        } catch(PDOException $e) {
            $message = "Error: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Fetch program data
$program = null;
$officials = array();

if (!$db_error) {
    try {
        // Fetch program
        $stmt = $conn->prepare("SELECT * FROM assistance_programs WHERE program_id = ?");
        $stmt->execute([$program_id]);
        $program = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$program) {
            $_SESSION['message'] = "Program not found.";
            $_SESSION['message_type'] = "danger";
            header("Location: programs.php");
            exit;
        }
        
        // Get officials for coordinator dropdown
        $officials_query = "SELECT o.official_id, r.first_name, r.last_name, o.position 
                           FROM officials o 
                           LEFT JOIN residents r ON o.resident_id = r.resident_id
                           WHERE o.status = 'Active'
                           ORDER BY o.position, r.last_name";
        $officials_stmt = $conn->query($officials_query);
        if ($officials_stmt) {
            $officials = $officials_stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch(PDOException $e) {
        $_SESSION['message'] = "Error: " . $e->getMessage();
        $_SESSION['message_type'] = "danger";
        header("Location: programs.php");
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🎁 Edit Assistance Program</h1>
                    <a href="programs.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Programs
                    </a>
                </div>

                <?php /* Removed traditional alert - now using toast notifications */ ?>

                <?php if ($db_error): ?>
                <div class="alert alert-danger">
                    <strong>⚠️ Database Error:</strong> Could not connect to the database. Please contact the system administrator.
                </div>
                <?php else: ?>
                
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">📋 Edit Program: <?php echo htmlspecialchars($program['program_name']); ?></h6>
                    </div>
                    <div class="card-body">
                        <form action="" method="post" class="needs-validation" novalidate>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="program_name" class="form-label">🏷️ Program Name</label>
                                    <input type="text" class="form-control" id="program_name" name="program_name" value="<?php echo htmlspecialchars($program['program_name']); ?>" required>
                                    <div class="invalid-feedback">
                                        Please provide a program name.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="program_type" class="form-label">🔖 Program Type</label>
                                    <select class="form-select" id="program_type" name="program_type" required>
                                        <option value="">-- Select Type --</option>
                                        <option value="Educational" <?php echo ($program['program_type'] == 'Educational') ? 'selected' : ''; ?>>🎓 Educational</option>
                                        <option value="Medical" <?php echo ($program['program_type'] == 'Medical') ? 'selected' : ''; ?>>🏥 Medical</option>
                                        <option value="Financial" <?php echo ($program['program_type'] == 'Financial') ? 'selected' : ''; ?>>💵 Financial</option>
                                        <option value="Relief" <?php echo ($program['program_type'] == 'Relief') ? 'selected' : ''; ?>>🧰 Relief</option>
                                        <option value="Senior Citizen" <?php echo ($program['program_type'] == 'Senior Citizen') ? 'selected' : ''; ?>>👵 Senior Citizen</option>
                                        <option value="PWD" <?php echo ($program['program_type'] == 'PWD') ? 'selected' : ''; ?>>♿ PWD</option>
                                        <option value="Other" <?php echo ($program['program_type'] == 'Other') ? 'selected' : ''; ?>>📋 Other</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a program type.
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="budget" class="form-label">💰 Budget (₱)</label>
                                    <input type="number" step="0.01" class="form-control" id="budget" name="budget" value="<?php echo $program['budget']; ?>">
                                </div>
                                <div class="col-md-4">
                                    <label for="start_date" class="form-label">📅 Start Date</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $program['start_date']; ?>">
                                </div>
                                <div class="col-md-4">
                                    <label for="end_date" class="form-label">📅 End Date</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $program['end_date']; ?>">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">📝 Description</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($program['description']); ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="beneficiary_criteria" class="form-label">👥 Beneficiary Criteria</label>
                                <textarea class="form-control" id="beneficiary_criteria" name="beneficiary_criteria" rows="3"><?php echo htmlspecialchars($program['beneficiary_criteria']); ?></textarea>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">🔄 Status</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="Active" <?php echo ($program['status'] == 'Active') ? 'selected' : ''; ?>>✅ Active</option>
                                        <option value="Pending" <?php echo ($program['status'] == 'Pending') ? 'selected' : ''; ?>>⏳ Pending</option>
                                        <option value="Completed" <?php echo ($program['status'] == 'Completed') ? 'selected' : ''; ?>>🏁 Completed</option>
                                        <option value="Cancelled" <?php echo ($program['status'] == 'Cancelled') ? 'selected' : ''; ?>>❌ Cancelled</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a status.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="coordinator_id" class="form-label">👤 Coordinator</label>
                                    <select class="form-select" id="coordinator_id" name="coordinator_id">
                                        <option value="">-- Select Coordinator --</option>
                                        <?php foreach($officials as $official): ?>
                                        <option value="<?php echo $official['official_id']; ?>" <?php echo ($program['coordinator_id'] == $official['official_id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($official['last_name'] . ', ' . $official['first_name'] . ' - ' . $official['position']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="programs.php" class="btn btn-secondary me-md-2">❌ Cancel</a>
                                <button type="submit" name="edit_program" class="btn btn-primary">💾 Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
        <div id="successToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>
                    <span id="successMessage"></span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
        <div id="errorToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span id="errorMessage"></span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
        <div id="dangerToast" class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <span id="dangerMessage"></span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
        <div id="warningToast" class="toast align-items-center text-white bg-warning border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="warningMessage"></span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
        <div id="infoToast" class="toast align-items-center text-white bg-info border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-info-circle me-2"></i>
                    <span id="infoMessage"></span>
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Include jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Toast notification function
        function showToast(message, type = 'success') {
            const toastId = type + 'Toast';
            const messageId = type + 'Message';

            // Set the message
            document.getElementById(messageId).textContent = message;

            // Show the toast
            const toast = new bootstrap.Toast(document.getElementById(toastId), {
                autohide: true,
                delay: 5000
            });
            toast.show();
        }

        $(document).ready(function() {
            // Show toast if there's a message from PHP
            <?php if (!empty($message)): ?>
                showToast('<?php echo addslashes($message); ?>', '<?php echo $message_type; ?>');
            <?php endif; ?>
            // Form validation
            (function() {
                'use strict';
                window.addEventListener('load', function() {
                    // Fetch all the forms we want to apply custom Bootstrap validation styles to
                    var forms = document.getElementsByClassName('needs-validation');
                    // Loop over them and prevent submission
                    var validation = Array.prototype.filter.call(forms, function(form) {
                        form.addEventListener('submit', function(event) {
                            if (form.checkValidity() === false) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);
            })();

            // Date validation for start and end dates
            $('#end_date').on('change', function() {
                const startDate = $('#start_date').val();
                const endDate = $(this).val();
                
                if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
                    alert('End date cannot be earlier than start date.');
                    $(this).val('');
                }
            });
        });
    </script>
</body>
</html> 